import { notFound } from 'next/navigation'
import { Locale } from '@/config/global'
import type { Metadata } from 'next'
import { genPageMetadata } from '@/lib/seo'
import { useTranslations } from 'next-intl'
import { Video, Download, Zap } from 'lucide-react'

// 页面元数据配置
const METADATA: Record<Locale, { title: string; description: string }> = {
  en: {
    title: 'SnapAny Video Download Extension',
    description: 'Install the extension to quickly download videos from any site. Easy to use, safe, and completely free—forever.'
  },
  zh: {
    title: 'SnapAny 视频下载插件',
    description: '安装 SnapAny 插件，轻松保存网页视频。操作便捷，安全可靠，永久免费使用。'
  },
  ja: {
    title: 'SnapAny ビデオダウンロード拡張機能',
    description: '拡張機能をインストールして、あらゆるサイトから動画を素早くダウンロード。使いやすく、安全で、完全に無料—永続的に。'
  },
  es: {
    title: 'SnapAny Extensión de Descarga de Videos',
    description: 'Instala la extensión para descargar rápidamente videos de cualquier sitio. Fácil de usar, seguro y completamente gratis—para siempre.'
  }
}

export async function generateMetadata({ params }: { params: { locale: Locale } }): Promise<Metadata> {
  const metadata = METADATA[params.locale]
  if (!metadata) {
    notFound()
  }

  return genPageMetadata({
    title: metadata.title,
    description: metadata.description,
    pathname: '/downloader',
    locale: params.locale,
  })
}

export default function DownloaderPage({ params }: { params: { locale: Locale } }) {
  const t = useTranslations('downloader')

  return (
    <main className="mb-8">
      {/* Hero Section with existing background */}
      <section className="bg-white dark:bg-gray-900 bg-[url('/images/hero-pattern.svg')] dark:bg-[url('/images/hero-pattern-dark.svg')]">
        <div className="bg-gradient-to-b from-blue-50 to-transparent dark:from-blue-900 w-full h-full">
          <div className="container flex flex-col items-center py-12 sm:py-16 gap-4">
            <div className="flex flex-col gap-4 items-center text-center">
              <h1 className="text-gray-900 tracking-tight text-3xl sm:text-4xl lg:text-5xl font-bold">
                {t('title')}
              </h1>
              <h2 className="text-gray-500 text-lg lg:text-xl">
                {t('subtitle')}
              </h2>
            </div>

            {/* Placeholder for downloader component */}
            <div className="w-full max-w-4xl mt-8">
              <div className="flex flex-col justify-center items-center p-6 gap-4 relative w-full h-80 bg-white shadow-lg rounded-lg">
                <div className="flex flex-col items-center gap-4">
                  <div className="text-gray-400 text-lg">
                    {t('placeholder')}
                  </div>
                  <div className="text-gray-500 text-sm text-center max-w-md">
                    {t('placeholderDesc')}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-12 max-w-6xl mx-auto">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Video className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900">自动识别网页视频</h3>
              <p className="text-gray-600 text-sm leading-relaxed">无需复杂操作，SnapAny 会自动检测网页中的视频或音频资源，精准识别各类网页下的内容。</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Download className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900">一键轻松保存</h3>
              <p className="text-gray-600 text-sm leading-relaxed">点击检测到后，只需点击一下，即可开始下载。操作简单，无需繁琐步骤。</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Zap className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900">下载快速且稳定</h3>
              <p className="text-gray-600 text-sm leading-relaxed">无论是大文件还是长视频，SnapAny 都能稳定高速地完成下载，不卡顿。</p>
            </div>
          </div>
        </div>
      </section>

      {/* How to Use Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">{t('howToUseTitle')}</h2>
          <div className="max-w-3xl mx-auto">
            <div className="space-y-8">
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0">
                  1
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">{t('step1Title')}</h3>
                  <p className="text-gray-600">{t('step1Desc')}</p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0">
                  2
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">{t('step2Title')}</h3>
                  <p className="text-gray-600">{t('step2Desc')}</p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0">
                  3
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-2">{t('step3Title')}</h3>
                  <p className="text-gray-600">{t('step3Desc')}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}